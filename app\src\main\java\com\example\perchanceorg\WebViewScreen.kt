package com.example.perchanceorg

import android.annotation.SuppressLint
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebSettings
import android.webkit.WebChromeClient
import android.graphics.Bitmap
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewScreen(
    url: String = "https://perchance.org",
    modifier: Modifier = Modifier
) {
    val webViewState = rememberWebViewState()
    var webView by remember { mutableStateOf<WebView?>(null) }

    val handleAction = { action: WebViewAction ->
        when (action) {
            is WebViewAction.GoBack -> webView?.goBack()
            is WebViewAction.GoForward -> webView?.goForward()
            is WebViewAction.Reload -> webView?.reload()
            is WebViewAction.LoadUrl -> webView?.loadUrl(action.url)
            is WebViewAction.ClearError -> {
                webViewState.value = webViewState.value.copy(loadError = null)
            }
        }
    }
    
    Column(modifier = modifier.fillMaxSize()) {
        // Top bar with navigation controls
        if (webViewState.value.canGoBack || webViewState.value.canGoForward) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                horizontalArrangement = Arrangement.Start
            ) {
                Button(
                    onClick = { handleAction(WebViewAction.GoBack) },
                    enabled = webViewState.value.canGoBack,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text("← Назад")
                }
                Button(
                    onClick = { handleAction(WebViewAction.GoForward) },
                    enabled = webViewState.value.canGoForward,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text("→ Вперед")
                }
                Button(
                    onClick = { handleAction(WebViewAction.Reload) }
                ) {
                    Text("⟳ Оновити")
                }
            }
        }
        
        // Loading indicator with progress
        if (webViewState.value.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (webViewState.value.progress > 0) {
                        LinearProgressIndicator(
                            progress = webViewState.value.progress / 100f,
                            modifier = Modifier.fillMaxWidth()
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("Завантаження... ${webViewState.value.progress}%")
                    } else {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("Завантаження...")
                    }
                    webViewState.value.url?.let { currentUrl ->
                        Text(
                            text = currentUrl,
                            style = MaterialTheme.typography.bodySmall,
                            maxLines = 1
                        )
                    }
                }
            }
        }
        
        // Error message
        webViewState.value.loadError?.let { error ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Помилка завантаження",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Text(
                        text = error,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row {
                        Button(
                            onClick = {
                                handleAction(WebViewAction.ClearError)
                                handleAction(WebViewAction.Reload)
                            },
                            modifier = Modifier.padding(end = 8.dp)
                        ) {
                            Text("Спробувати знову")
                        }
                        OutlinedButton(
                            onClick = {
                                handleAction(WebViewAction.ClearError)
                                handleAction(WebViewAction.LoadUrl("https://perchance.org"))
                            }
                        ) {
                            Text("На головну")
                        }
                    }
                }
            }
        }
        
        // WebView
        AndroidView(
            factory = { context ->
                WebView(context).apply {
                    webView = this
                    
                    // Configure WebView settings
                    settings.apply {
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        loadWithOverviewMode = true
                        useWideViewPort = true
                        builtInZoomControls = true
                        displayZoomControls = false
                        setSupportZoom(true)
                        cacheMode = WebSettings.LOAD_DEFAULT
                        mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        userAgentString = "Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
                    }
                    
                    // Set WebViewClient to handle page navigation
                    webViewClient = object : WebViewClient() {
                        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                            super.onPageStarted(view, url, favicon)
                            webViewState.value = webViewState.value.copy(
                                isLoading = true,
                                loadError = null,
                                url = url
                            )
                        }

                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            webViewState.value = webViewState.value.copy(
                                isLoading = false,
                                canGoBack = view?.canGoBack() ?: false,
                                canGoForward = view?.canGoForward() ?: false,
                                url = url,
                                title = view?.title
                            )
                        }

                        override fun onReceivedError(
                            view: WebView?,
                            errorCode: Int,
                            description: String?,
                            failingUrl: String?
                        ) {
                            super.onReceivedError(view, errorCode, description, failingUrl)
                            webViewState.value = webViewState.value.copy(
                                isLoading = false,
                                loadError = description ?: "Невідома помилка (код: $errorCode)"
                            )
                        }
                        
                        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                            // Allow navigation within perchance.org domain
                            return if (url?.contains("perchance.org") == true) {
                                false // Let WebView handle it
                            } else {
                                // For external links, you might want to open in external browser
                                false // For now, allow all navigation
                            }
                        }
                    }
                    
                    // Set WebChromeClient for better JavaScript support
                    webChromeClient = object : WebChromeClient() {
                        override fun onProgressChanged(view: WebView?, newProgress: Int) {
                            super.onProgressChanged(view, newProgress)
                            webViewState.value = webViewState.value.copy(
                                progress = newProgress
                            )
                        }

                        override fun onReceivedTitle(view: WebView?, title: String?) {
                            super.onReceivedTitle(view, title)
                            webViewState.value = webViewState.value.copy(
                                title = title
                            )
                        }
                    }
                    
                    // Load the URL
                    loadUrl(url)
                }
            },
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        )
    }
}
