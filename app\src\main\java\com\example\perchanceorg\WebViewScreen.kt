package com.example.perchanceorg

import android.annotation.SuppressLint
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebSettings
import android.webkit.WebChromeClient
import android.webkit.ConsoleMessage
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebResourceError
import android.graphics.Bitmap
import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewScreen(
    url: String = "https://perchance.org/welcome",
    modifier: Modifier = Modifier
) {
    val webViewState = rememberWebViewState()
    var webView by remember { mutableStateOf<WebView?>(null) }

    val handleAction = { action: WebViewAction ->
        when (action) {
            is WebViewAction.GoBack -> webView?.goBack()
            is WebViewAction.GoForward -> webView?.goForward()
            is WebViewAction.Reload -> webView?.reload()
            is WebViewAction.LoadUrl -> webView?.loadUrl(action.url)
            is WebViewAction.ClearError -> {
                webViewState.value = webViewState.value.copy(loadError = null)
            }
            is WebViewAction.ForceRefresh -> {
                webView?.clearCache(true)
                webView?.reload()
            }
            is WebViewAction.LoadAlternativeUrl -> {
                webView?.loadUrl("https://perchance.org")
            }
        }
    }
    
    Column(modifier = modifier.fillMaxSize()) {
        // Top bar with navigation controls
        if (webViewState.value.canGoBack || webViewState.value.canGoForward) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                horizontalArrangement = Arrangement.Start
            ) {
                Button(
                    onClick = { handleAction(WebViewAction.GoBack) },
                    enabled = webViewState.value.canGoBack,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text("← Назад")
                }
                Button(
                    onClick = { handleAction(WebViewAction.GoForward) },
                    enabled = webViewState.value.canGoForward,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text("→ Вперед")
                }
                Button(
                    onClick = { handleAction(WebViewAction.Reload) }
                ) {
                    Text("⟳ Оновити")
                }
            }
        }
        
        // Loading indicator with progress
        if (webViewState.value.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (webViewState.value.progress > 0) {
                        LinearProgressIndicator(
                            progress = webViewState.value.progress / 100f,
                            modifier = Modifier.fillMaxWidth()
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("Завантаження... ${webViewState.value.progress}%")
                    } else {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("Завантаження...")
                    }
                    webViewState.value.url?.let { currentUrl ->
                        Text(
                            text = currentUrl,
                            style = MaterialTheme.typography.bodySmall,
                            maxLines = 1
                        )
                    }
                }
            }
        }
        
        // Error message
        webViewState.value.loadError?.let { error ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Помилка завантаження",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Text(
                        text = error,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row {
                        Button(
                            onClick = {
                                handleAction(WebViewAction.ClearError)
                                handleAction(WebViewAction.Reload)
                            },
                            modifier = Modifier.padding(end = 8.dp)
                        ) {
                            Text("Спробувати знову")
                        }
                        OutlinedButton(
                            onClick = {
                                handleAction(WebViewAction.ClearError)
                                handleAction(WebViewAction.LoadAlternativeUrl)
                            }
                        ) {
                            Text("На головну")
                        }
                    }
                }
            }
        }

        // Content visibility warning
        if (!webViewState.value.isLoading &&
            webViewState.value.loadError == null &&
            !webViewState.value.isContentVisible &&
            webViewState.value.lastLoadTime > 0 &&
            System.currentTimeMillis() - webViewState.value.lastLoadTime > 3000) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Контент може не відображатися",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                    Text(
                        text = "Спробуйте оновити сторінку або очистити кеш",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row {
                        Button(
                            onClick = { handleAction(WebViewAction.ForceRefresh) },
                            modifier = Modifier.padding(end = 8.dp)
                        ) {
                            Text("Очистити кеш")
                        }
                        OutlinedButton(
                            onClick = { handleAction(WebViewAction.LoadAlternativeUrl) }
                        ) {
                            Text("Перезавантажити")
                        }
                    }
                }
            }
        }

        // WebView
        AndroidView(
            factory = { context ->
                WebView(context).apply {
                    webView = this

                    // Configure WebView using utility function
                    WebViewUtils.configureWebViewForPerchance(this)
                    
                    // Set WebViewClient to handle page navigation
                    webViewClient = object : WebViewClient() {
                        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                            super.onPageStarted(view, url, favicon)
                            Log.d("WebView", "Page started loading: $url")
                            webViewState.value = webViewState.value.copy(
                                isLoading = true,
                                loadError = null,
                                url = url
                            )
                        }

                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            Log.d("WebView", "Page finished loading: $url")
                            Log.d("WebView", "Page title: ${view?.title}")
                            Log.d("WebView", "Page height: ${view?.contentHeight}")

                            // Check if content is visible after a short delay
                            view?.postDelayed({
                                val contentHeight = view.contentHeight
                                val isContentVisible = contentHeight > 0 && !view.title.isNullOrEmpty()
                                Log.d("WebView", "Content visibility check - Height: $contentHeight, Visible: $isContentVisible")

                                // Log detailed WebView info for debugging
                                WebViewUtils.logWebViewInfo(view)

                                webViewState.value = webViewState.value.copy(
                                    isLoading = false,
                                    canGoBack = view.canGoBack(),
                                    canGoForward = view.canGoForward(),
                                    url = url,
                                    title = view.title,
                                    isContentVisible = isContentVisible,
                                    lastLoadTime = System.currentTimeMillis()
                                )
                            }, 2000) // Wait 2 seconds for content to render
                        }

                        override fun onReceivedError(
                            view: WebView?,
                            errorCode: Int,
                            description: String?,
                            failingUrl: String?
                        ) {
                            super.onReceivedError(view, errorCode, description, failingUrl)
                            Log.e("WebView", "Error loading page: $description (code: $errorCode) for URL: $failingUrl")
                            webViewState.value = webViewState.value.copy(
                                isLoading = false,
                                loadError = description ?: "Невідома помилка (код: $errorCode)"
                            )
                        }

                        override fun onReceivedHttpError(
                            view: WebView?,
                            request: WebResourceRequest?,
                            errorResponse: WebResourceResponse?
                        ) {
                            super.onReceivedHttpError(view, request, errorResponse)
                            Log.e("WebView", "HTTP Error: ${errorResponse?.statusCode} for ${request?.url}")
                        }

                        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                            val url = request?.url?.toString()
                            Log.d("WebView", "Navigating to: $url")
                            return if (url?.contains("perchance.org") == true) {
                                false // Let WebView handle it
                            } else {
                                false // For now, allow all navigation
                            }
                        }
                        
                        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                            // Allow navigation within perchance.org domain
                            return if (url?.contains("perchance.org") == true) {
                                false // Let WebView handle it
                            } else {
                                // For external links, you might want to open in external browser
                                false // For now, allow all navigation
                            }
                        }
                    }
                    
                    // Set WebChromeClient for better JavaScript support
                    webChromeClient = object : WebChromeClient() {
                        override fun onProgressChanged(view: WebView?, newProgress: Int) {
                            super.onProgressChanged(view, newProgress)
                            Log.d("WebView", "Progress: $newProgress%")
                            webViewState.value = webViewState.value.copy(
                                progress = newProgress
                            )
                        }

                        override fun onReceivedTitle(view: WebView?, title: String?) {
                            super.onReceivedTitle(view, title)
                            Log.d("WebView", "Title received: $title")
                            webViewState.value = webViewState.value.copy(
                                title = title
                            )
                        }

                        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                            Log.d("WebView", "Console: ${consoleMessage?.message()} at ${consoleMessage?.sourceId()}:${consoleMessage?.lineNumber()}")
                            return true
                        }
                    }
                    
                    // Load the URL
                    loadUrl(url)
                }
            },
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        )
    }
}
