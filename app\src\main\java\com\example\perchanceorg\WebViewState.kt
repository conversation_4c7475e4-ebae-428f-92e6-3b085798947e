package com.example.perchanceorg

import androidx.compose.runtime.*

data class WebViewState(
    val isLoading: <PERSON>olean = false,
    val loadError: String? = null,
    val canGoBack: Boolean = false,
    val canGoForward: Boolean = false,
    val url: String? = null,
    val title: String? = null,
    val progress: Int = 0
)

@Composable
fun rememberWebViewState(): MutableState<WebViewState> {
    return remember { mutableStateOf(WebViewState()) }
}

sealed class WebViewAction {
    object GoBack : WebViewAction()
    object GoForward : WebViewAction()
    object Reload : WebViewAction()
    data class LoadUrl(val url: String) : WebViewAction()
    object ClearError : WebViewAction()
}
