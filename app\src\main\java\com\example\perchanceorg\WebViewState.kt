package com.example.perchanceorg

import androidx.compose.runtime.*

data class WebViewState(
    val isLoading: <PERSON>olean = false,
    val loadError: String? = null,
    val canGoBack: Boolean = false,
    val canGoForward: Boolean = false,
    val url: String? = null,
    val title: String? = null,
    val progress: Int = 0,
    val isContentVisible: Boolean = false,
    val lastLoadTime: Long = 0L
)

@Composable
fun rememberWebViewState(): MutableState<WebViewState> {
    return remember { mutableStateOf(WebViewState()) }
}

sealed class WebViewAction {
    object GoBack : WebViewAction()
    object GoForward : WebViewAction()
    object Reload : WebViewAction()
    data class LoadUrl(val url: String) : WebViewAction()
    object ClearError : WebViewAction()
    object ForceRefresh : WebViewAction()
    object LoadAlternativeUrl : WebViewAction()
}
