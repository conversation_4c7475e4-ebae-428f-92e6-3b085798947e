package com.example.perchanceorg

import android.webkit.WebView
import android.webkit.WebSettings
import android.util.Log

object WebViewUtils {
    
    fun enableWebViewDebugging() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true)
            Log.d("WebView", "WebView debugging enabled")
        }
    }
    
    fun logWebViewInfo(webView: WebView) {
        Log.d("WebView", "=== WebView Info ===")
        Log.d("WebView", "URL: ${webView.url}")
        Log.d("WebView", "Title: ${webView.title}")
        Log.d("WebView", "Content Height: ${webView.contentHeight}")
        Log.d("WebView", "Progress: ${webView.progress}")
        Log.d("WebView", "Can Go Back: ${webView.canGoBack()}")
        Log.d("WebView", "Can Go Forward: ${webView.canGoForward()}")
        Log.d("WebView", "User Agent: ${webView.settings.userAgentString}")
        Log.d("WebView", "JavaScript Enabled: ${webView.settings.javaScriptEnabled}")
        Log.d("WebView", "DOM Storage Enabled: ${webView.settings.domStorageEnabled}")
        Log.d("WebView", "==================")
    }
    
    fun getOptimalUserAgent(): String {
        return "Mozilla/5.0 (Linux; Android 12; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"
    }
    
    fun configureWebViewForPerchance(webView: WebView) {
        enableWebViewDebugging()
        
        webView.settings.apply {
            // Basic settings
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            
            // Zoom settings
            builtInZoomControls = true
            displayZoomControls = false
            setSupportZoom(true)
            
            // Cache and loading
            cacheMode = WebSettings.LOAD_DEFAULT
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            
            // Security settings
            allowFileAccess = false
            allowContentAccess = false
            
            // JavaScript settings
            javaScriptCanOpenWindowsAutomatically = true
            setSupportMultipleWindows(false)
            
            // Media settings
            mediaPlaybackRequiresUserGesture = false
            
            // User agent
            userAgentString = getOptimalUserAgent()
            
            Log.d("WebView", "WebView configured for Perchance")
        }
    }
    
    fun getTestUrls(): List<Pair<String, String>> {
        return listOf(
            "Головна" to "https://perchance.org",
            "Welcome" to "https://perchance.org/welcome",
            "AI Chat" to "https://perchance.org/ai-chat",
            "Text Generator" to "https://perchance.org/text-generator",
            "Image Generator" to "https://perchance.org/ai-text-to-image-generator"
        )
    }
}
