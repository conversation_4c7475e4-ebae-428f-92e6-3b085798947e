{"logs": [{"outputFile": "com.example.perchanceorg.app-mergeDebugResources-2:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\65521d5f343b1f7bc6a303101c338b4b\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,199,301,398,499,606,713,8381", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "194,296,393,494,601,708,823,8477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8750,8851", "endColumns": "100,102", "endOffsets": "8846,8949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,1008,1095,1169,1244,1321,1398,1475,1545", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,1003,1090,1164,1239,1316,1393,1470,1540,1661"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,922,1003,1104,1205,1291,1372,7733,7824,7906,7991,8078,8152,8227,8304,8482,8559,8629", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "917,998,1099,1200,1286,1367,1468,7819,7901,7986,8073,8147,8222,8299,8376,8554,8624,8745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,402,516,616,715,831,972,1088,1239,1325,1425,1518,1620,1738,1865,1970,2100,2229,2365,2530,2659,2783,2912,3021,3115,3211,3334,3462,3559,3671,3781,3913,4054,4166,4266,4345,4441,4538,4653,4740,4825,4939,5019,5102,5201,5301,5396,5495,5583,5688,5788,5891,6007,6087,6205", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "166,281,397,511,611,710,826,967,1083,1234,1320,1420,1513,1615,1733,1860,1965,2095,2224,2360,2525,2654,2778,2907,3016,3110,3206,3329,3457,3554,3666,3776,3908,4049,4161,4261,4340,4436,4533,4648,4735,4820,4934,5014,5097,5196,5296,5391,5490,5578,5683,5783,5886,6002,6082,6200,6310"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1473,1589,1704,1820,1934,2034,2133,2249,2390,2506,2657,2743,2843,2936,3038,3156,3283,3388,3518,3647,3783,3948,4077,4201,4330,4439,4533,4629,4752,4880,4977,5089,5199,5331,5472,5584,5684,5763,5859,5956,6071,6158,6243,6357,6437,6520,6619,6719,6814,6913,7001,7106,7206,7309,7425,7505,7623", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "1584,1699,1815,1929,2029,2128,2244,2385,2501,2652,2738,2838,2931,3033,3151,3278,3383,3513,3642,3778,3943,4072,4196,4325,4434,4528,4624,4747,4875,4972,5084,5194,5326,5467,5579,5679,5758,5854,5951,6066,6153,6238,6352,6432,6515,6614,6714,6809,6908,6996,7101,7201,7304,7420,7500,7618,7728"}}]}]}