  SuppressLint android.annotation  Activity android.app  Modifier android.app.Activity  PerchanceorgTheme android.app.Activity  Scaffold android.app.Activity  
WebViewScreen android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  Context android.content  Modifier android.content.Context  PerchanceorgTheme android.content.Context  Scaffold android.content.Context  
WebViewScreen android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  
setContent android.content.Context  Modifier android.content.ContextWrapper  PerchanceorgTheme android.content.ContextWrapper  Scaffold android.content.ContextWrapper  
WebViewScreen android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bitmap android.graphics  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Modifier  android.view.ContextThemeWrapper  PerchanceorgTheme  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  
WebViewScreen  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  WebChromeClient android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  onProgressChanged android.webkit.WebChromeClient  onReceivedTitle android.webkit.WebChromeClient  LOAD_DEFAULT android.webkit.WebSettings  MIXED_CONTENT_ALWAYS_ALLOW android.webkit.WebSettings  WebSettings android.webkit.WebSettings  apply android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  WebSettings android.webkit.WebView  apply android.webkit.WebView  	canGoBack android.webkit.WebView  canGoForward android.webkit.WebView  contains android.webkit.WebView  goBack android.webkit.WebView  	goForward android.webkit.WebView  loadUrl android.webkit.WebView  reload android.webkit.WebView  settings android.webkit.WebView  title android.webkit.WebView  webChromeClient android.webkit.WebView  
webViewClient android.webkit.WebView  contains android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  
onPageStarted android.webkit.WebViewClient  onReceivedError android.webkit.WebViewClient  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  PerchanceorgTheme #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  
WebViewScreen #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Modifier -androidx.activity.ComponentActivity.Companion  PerchanceorgTheme -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  
WebViewScreen -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  AndroidView "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Bitmap "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SuppressLint "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  WebChromeClient "androidx.compose.foundation.layout  WebSettings "androidx.compose.foundation.layout  WebView "androidx.compose.foundation.layout  
WebViewAction "androidx.compose.foundation.layout  
WebViewClient "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  LinearProgressIndicator +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AndroidView .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  WebSettings .androidx.compose.foundation.layout.ColumnScope  WebView .androidx.compose.foundation.layout.ColumnScope  
WebViewAction .androidx.compose.foundation.layout.ColumnScope  apply .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  contains .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
WebViewAction +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  
ClearError 0androidx.compose.foundation.layout.WebViewAction  GoBack 0androidx.compose.foundation.layout.WebViewAction  	GoForward 0androidx.compose.foundation.layout.WebViewAction  LoadUrl 0androidx.compose.foundation.layout.WebViewAction  Reload 0androidx.compose.foundation.layout.WebViewAction  	Alignment androidx.compose.material3  AndroidView androidx.compose.material3  Arrangement androidx.compose.material3  Bitmap androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Int androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OutlinedButton androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  SuppressLint androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  WebChromeClient androidx.compose.material3  WebSettings androidx.compose.material3  WebView androidx.compose.material3  
WebViewAction androidx.compose.material3  
WebViewClient androidx.compose.material3  apply androidx.compose.material3  
cardColors androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
ClearError (androidx.compose.material3.WebViewAction  GoBack (androidx.compose.material3.WebViewAction  	GoForward (androidx.compose.material3.WebViewAction  LoadUrl (androidx.compose.material3.WebViewAction  Reload (androidx.compose.material3.WebViewAction  	Alignment androidx.compose.runtime  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  Bitmap androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Int androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedButton androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  SuppressLint androidx.compose.runtime  Text androidx.compose.runtime  WebChromeClient androidx.compose.runtime  WebSettings androidx.compose.runtime  WebView androidx.compose.runtime  
WebViewAction androidx.compose.runtime  
WebViewClient androidx.compose.runtime  WebViewState androidx.compose.runtime  apply androidx.compose.runtime  
cardColors androidx.compose.runtime  contains androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  
ClearError &androidx.compose.runtime.WebViewAction  GoBack &androidx.compose.runtime.WebViewAction  	GoForward &androidx.compose.runtime.WebViewAction  LoadUrl &androidx.compose.runtime.WebViewAction  Reload &androidx.compose.runtime.WebViewAction  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  PerchanceorgTheme #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  
WebViewScreen #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	Alignment com.example.perchanceorg  AndroidView com.example.perchanceorg  Arrangement com.example.perchanceorg  Bitmap com.example.perchanceorg  Boolean com.example.perchanceorg  Box com.example.perchanceorg  Bundle com.example.perchanceorg  Button com.example.perchanceorg  Card com.example.perchanceorg  CardDefaults com.example.perchanceorg  CircularProgressIndicator com.example.perchanceorg  Column com.example.perchanceorg  ComponentActivity com.example.perchanceorg  
Composable com.example.perchanceorg  Int com.example.perchanceorg  LinearProgressIndicator com.example.perchanceorg  MainActivity com.example.perchanceorg  
MaterialTheme com.example.perchanceorg  Modifier com.example.perchanceorg  MutableState com.example.perchanceorg  OutlinedButton com.example.perchanceorg  PerchanceorgTheme com.example.perchanceorg  Preview com.example.perchanceorg  Row com.example.perchanceorg  Scaffold com.example.perchanceorg  Spacer com.example.perchanceorg  String com.example.perchanceorg  SuppressLint com.example.perchanceorg  Text com.example.perchanceorg  WebChromeClient com.example.perchanceorg  WebSettings com.example.perchanceorg  WebView com.example.perchanceorg  
WebViewAction com.example.perchanceorg  
WebViewClient com.example.perchanceorg  WebViewPreview com.example.perchanceorg  
WebViewScreen com.example.perchanceorg  WebViewState com.example.perchanceorg  apply com.example.perchanceorg  
cardColors com.example.perchanceorg  contains com.example.perchanceorg  fillMaxSize com.example.perchanceorg  fillMaxWidth com.example.perchanceorg  getValue com.example.perchanceorg  height com.example.perchanceorg  let com.example.perchanceorg  mutableStateOf com.example.perchanceorg  padding com.example.perchanceorg  provideDelegate com.example.perchanceorg  remember com.example.perchanceorg  rememberWebViewState com.example.perchanceorg  setValue com.example.perchanceorg  size com.example.perchanceorg  Modifier %com.example.perchanceorg.MainActivity  PerchanceorgTheme %com.example.perchanceorg.MainActivity  Scaffold %com.example.perchanceorg.MainActivity  
WebViewScreen %com.example.perchanceorg.MainActivity  enableEdgeToEdge %com.example.perchanceorg.MainActivity  fillMaxSize %com.example.perchanceorg.MainActivity  padding %com.example.perchanceorg.MainActivity  
setContent %com.example.perchanceorg.MainActivity  
ClearError &com.example.perchanceorg.WebViewAction  GoBack &com.example.perchanceorg.WebViewAction  	GoForward &com.example.perchanceorg.WebViewAction  LoadUrl &com.example.perchanceorg.WebViewAction  Reload &com.example.perchanceorg.WebViewAction  String &com.example.perchanceorg.WebViewAction  
WebViewAction &com.example.perchanceorg.WebViewAction  url .com.example.perchanceorg.WebViewAction.LoadUrl  	canGoBack %com.example.perchanceorg.WebViewState  canGoForward %com.example.perchanceorg.WebViewState  copy %com.example.perchanceorg.WebViewState  	isLoading %com.example.perchanceorg.WebViewState  	loadError %com.example.perchanceorg.WebViewState  progress %com.example.perchanceorg.WebViewState  url %com.example.perchanceorg.WebViewState  Boolean !com.example.perchanceorg.ui.theme  Build !com.example.perchanceorg.ui.theme  
Composable !com.example.perchanceorg.ui.theme  DarkColorScheme !com.example.perchanceorg.ui.theme  
FontFamily !com.example.perchanceorg.ui.theme  
FontWeight !com.example.perchanceorg.ui.theme  LightColorScheme !com.example.perchanceorg.ui.theme  PerchanceorgTheme !com.example.perchanceorg.ui.theme  Pink40 !com.example.perchanceorg.ui.theme  Pink80 !com.example.perchanceorg.ui.theme  Purple40 !com.example.perchanceorg.ui.theme  Purple80 !com.example.perchanceorg.ui.theme  PurpleGrey40 !com.example.perchanceorg.ui.theme  PurpleGrey80 !com.example.perchanceorg.ui.theme  
Typography !com.example.perchanceorg.ui.theme  Unit !com.example.perchanceorg.ui.theme  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  apply kotlin  let kotlin  sp 
kotlin.Double  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  contains 
kotlin.String  let 
kotlin.String  contains kotlin.collections  contains 
kotlin.ranges  KMutableProperty0 kotlin.reflect  contains kotlin.sequences  contains kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              