# Troubleshooting WebView Issues

## Проблема: Білий екран після завантаження

Якщо ви бачите білий екран після завантаження сторінки perchance.org, спробуйте наступні кроки:

### 1. Перевірте логи
Відкрийте Android Studio Logcat і фільтруйте по тегу "WebView". Ви повинні побачити детальну інформацію про завантаження:

```
D/WebView: Page started loading: https://perchance.org/welcome
D/WebView: Progress: 10%
D/WebView: Progress: 50%
D/WebView: Progress: 100%
D/WebView: Page finished loading: https://perchance.org/welcome
D/WebView: Page title: Welcome to Perchance
D/WebView: Page height: 1234
D/WebView: Content visibility check - Height: 1234, Visible: true
```

### 2. Можливі причини білого екрану

#### A. JavaScript помилки
- Перевірте консольні повідомлення в логах
- Шукайте помилки типу "Console: Error at ..."

#### B. CSS/Стилі не завантажуються
- Перевірте HTTP помилки в логах
- Шукайте "HTTP Error: 404" або подібні

#### C. Проблеми з User Agent
- Деякі сайти блокують певні User Agent
- Спробуйте змінити User Agent в WebViewUtils.kt

#### D. Проблеми з JavaScript
- Переконайтеся, що JavaScript увімкнений
- Перевірте налаштування безпеки

### 3. Кроки для вирішення

#### Крок 1: Очистити кеш
Натисніть кнопку "Очистити кеш" в застосунку

#### Крок 2: Спробувати інший URL
Натисніть кнопку "🏠 Головна" для переходу на основну сторінку

#### Крок 3: Перевірити мережу
Переконайтеся, що у вас є стабільне інтернет-з'єднання

#### Крок 4: Перезапустити застосунок
Закрийте і знову відкрийте застосунок

### 4. Налаштування для розробників

#### Увімкнути WebView debugging
WebView debugging автоматично увімкнений в debug версії. Ви можете:

1. Відкрити Chrome на комп'ютері
2. Перейти на chrome://inspect
3. Знайти ваш пристрій і WebView
4. Натиснути "Inspect" для відкриття DevTools

#### Змінити User Agent
В файлі `WebViewUtils.kt` змініть функцію `getOptimalUserAgent()`:

```kotlin
fun getOptimalUserAgent(): String {
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
```

#### Тестувати різні URL
Використовуйте функцію `getTestUrls()` для тестування різних сторінок:

```kotlin
val testUrls = WebViewUtils.getTestUrls()
// Спробуйте кожен URL окремо
```

### 5. Відомі проблеми

#### Perchance.org використовує сучасні веб-технології
- Потребує JavaScript
- Використовує WebGL для деяких функцій
- Може потребувати localStorage

#### Рішення:
- Переконайтеся, що всі необхідні дозволи надані
- Перевірте, що WebView оновлений до останньої версії
- Спробуйте на різних пристроях/емуляторах

### 6. Контакт для підтримки

Якщо проблема не вирішується:
1. Зберіть логи з Logcat
2. Зробіть скріншот проблеми
3. Вкажіть модель пристрою та версію Android
4. Опишіть кроки для відтворення проблеми

### 7. Альтернативні рішення

Якщо WebView не працює:
1. Спробуйте відкрити сайт у зовнішньому браузері
2. Перевірте, чи працює сайт на інших пристроях
3. Спробуйте інші сторінки perchance.org
