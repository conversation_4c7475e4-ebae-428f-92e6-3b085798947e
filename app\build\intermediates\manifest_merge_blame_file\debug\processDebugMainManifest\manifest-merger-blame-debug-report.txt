1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.perchanceorg"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:5:5-67
11-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:6:5-79
12-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65521d5f343b1f7bc6a303101c338b4b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.perchanceorg.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65521d5f343b1f7bc6a303101c338b4b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65521d5f343b1f7bc6a303101c338b4b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.perchanceorg.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65521d5f343b1f7bc6a303101c338b4b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65521d5f343b1f7bc6a303101c338b4b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:8:5-31:19
21        android:allowBackup="true"
21-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65521d5f343b1f7bc6a303101c338b4b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="true"
26        android:fullBackupContent="@xml/backup_rules"
26-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:11:9-54
27        android:hardwareAccelerated="true"
27-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:18:9-43
28        android:icon="@mipmap/ic_launcher"
28-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:12:9-43
29        android:label="@string/app_name"
29-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:13:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:14:9-54
31        android:supportsRtl="true"
31-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:15:9-35
32        android:theme="@style/Theme.Perchanceorg"
32-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:16:9-50
33        android:usesCleartextTraffic="true" >
33-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:17:9-44
34        <activity
34-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:20:9-30:20
35            android:name="com.example.perchanceorg.MainActivity"
35-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:21:13-41
36            android:exported="true"
36-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:22:13-36
37            android:label="@string/app_name"
37-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:23:13-45
38            android:theme="@style/Theme.Perchanceorg" >
38-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:24:13-54
39            <intent-filter>
39-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:25:13-29:29
40                <action android:name="android.intent.action.MAIN" />
40-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:26:17-69
40-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:26:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:28:17-77
42-->A:\Programs\perchanceorg\app\src\main\AndroidManifest.xml:28:27-74
43            </intent-filter>
44        </activity>
45        <activity
45-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
46            android:name="androidx.compose.ui.tooling.PreviewActivity"
46-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
47            android:exported="true" />
47-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c67b3961ae102a76c39caaa9c3b06f68\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
48        <activity
48-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
49            android:name="androidx.activity.ComponentActivity"
49-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
50            android:exported="true" />
50-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff87ee284f9d5642da4cf893fd311eb\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
51
52        <provider
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.perchanceorg.androidx-startup"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f454ab72385d0dfc872c0c26b9212cd5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bacdd28a9efa3d7396cfe51acc42b86\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bacdd28a9efa3d7396cfe51acc42b86\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bacdd28a9efa3d7396cfe51acc42b86\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <receiver
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
68            android:name="androidx.profileinstaller.ProfileInstallReceiver"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
69            android:directBootAware="false"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
70            android:enabled="true"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
71            android:exported="true"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
72            android:permission="android.permission.DUMP" >
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
74                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
77                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
80                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
83                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d1397bf0f1b96c4a0772a6b2fe2a5a6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
84            </intent-filter>
85        </receiver>
86    </application>
87
88</manifest>
