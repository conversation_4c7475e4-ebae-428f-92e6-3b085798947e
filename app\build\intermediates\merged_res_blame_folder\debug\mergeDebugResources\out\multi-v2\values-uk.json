{"logs": [{"outputFile": "com.example.perchanceorg.app-mergeDebugResources-2:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,995,1083,1155,1231,1308,1385,1465,1535", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,75,76,76,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,990,1078,1150,1226,1303,1380,1460,1530,1653"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,925,1009,1111,1212,1296,1378,7767,7855,7937,8022,8110,8182,8258,8335,8513,8593,8663", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,75,76,76,79,69,122", "endOffsets": "920,1004,1106,1207,1291,1373,1462,7850,7932,8017,8105,8177,8253,8330,8407,8588,8658,8781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\65521d5f343b1f7bc6a303101c338b4b\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,408,509,614,719,8412", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "200,302,403,504,609,714,827,8508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1585,1701,1819,1937,2036,2131,2243,2381,2497,2644,2728,2828,2921,3017,3133,3257,3362,3503,3640,3775,3964,4091,4215,4344,4465,4559,4660,4786,4916,5014,5119,5228,5373,5524,5632,5732,5807,5902,5998,6117,6203,6290,6389,6469,6555,6654,6758,6853,6953,7042,7149,7245,7348,7466,7546,7661", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "1580,1696,1814,1932,2031,2126,2238,2376,2492,2639,2723,2823,2916,3012,3128,3252,3357,3498,3635,3770,3959,4086,4210,4339,4460,4554,4655,4781,4911,5009,5114,5223,5368,5519,5627,5727,5802,5897,5993,6112,6198,6285,6384,6464,6550,6649,6753,6848,6948,7037,7144,7240,7343,7461,7541,7656,7762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,99", "endOffsets": "147,247"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8786,8883", "endColumns": "96,99", "endOffsets": "8878,8978"}}]}]}